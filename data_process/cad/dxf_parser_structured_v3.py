import ezdxf
import json
import re
import os
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Optional, Tuple, Union
from ezdxf import bbox as ezdxf_bbox
from tqdm import tqdm


class DXFComprehensiveParser:
    """
    全面的DXF解析器 V3 - 更全面的CAD图纸格式信息解析
    支持坐标选项控制，保留所有内容而不精简
    """
    
    def __init__(self, dxf_path: str, include_coordinates: bool = True, include_raw_data: bool = True):
        """
        初始化解析器
        
        Args:
            dxf_path: DXF文件路径
            include_coordinates: 是否包含坐标信息
            include_raw_data: 是否包含原始数据
        """
        self.dxf_path = dxf_path
        self.include_coordinates = include_coordinates
        self.include_raw_data = include_raw_data
        self.doc = None
        self.all_entities = []
        self.text_entities = []
        self.geometric_entities = []
        self.dimension_entities = []
        self.block_entities = []
        
    def load_document(self) -> bool:
        """加载DXF文档"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            return True
        except Exception as e:
            print(f"错误: 无法读取DXF文件: {e}")
            return False
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        # 移除MTEXT格式化代码
        text = re.sub(r'\\[A-Za-z][0-9]*;?', '', text)
        text = re.sub(r'\\[{}]', '', text)
        text = re.sub(r'\\P', '\n', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def extract_comprehensive_entity_info(self, entity, space_name: str) -> Dict:
        """全面提取实体信息"""
        entity_type = entity.dxftype()
        
        # 基础信息
        entity_info = {
            "实体类型": entity_type,
            "图层": getattr(entity.dxf, 'layer', ''),
            "空间类型": space_name,
            "颜色": getattr(entity.dxf, 'color', 0),
            "线型": getattr(entity.dxf, 'linetype', ''),
            "线宽": getattr(entity.dxf, 'lineweight', 0),
            "可见性": not getattr(entity.dxf, 'invisible', False),
        }
        
        # 添加坐标信息（如果启用）
        if self.include_coordinates:
            entity_info["几何信息"] = self._extract_geometry_info(entity)
        
        # 添加原始DXF属性（如果启用）
        if self.include_raw_data:
            entity_info["原始属性"] = self._extract_raw_attributes(entity)
        
        # 根据实体类型提取特定信息
        if entity_type in ['TEXT', 'MTEXT', 'ATTRIB', 'ATTDEF']:
            entity_info.update(self._extract_text_info(entity))
        elif entity_type == 'INSERT':
            entity_info.update(self._extract_block_info(entity))
        elif entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE', 'CIRCLE', 'ARC', 'ELLIPSE']:
            entity_info.update(self._extract_geometric_info(entity))
        elif entity_type in ['DIMENSION', 'LEADER']:
            entity_info.update(self._extract_dimension_info(entity))
        elif entity_type == 'HATCH':
            entity_info.update(self._extract_hatch_info(entity))
        elif entity_type == 'SPLINE':
            entity_info.update(self._extract_spline_info(entity))
        
        return entity_info
    
    def _extract_geometry_info(self, entity) -> Dict:
        """提取几何信息"""
        geometry_info = {}
        entity_type = entity.dxftype()
        
        try:
            if hasattr(entity.dxf, 'insert'):
                geometry_info["插入点"] = list(entity.dxf.insert)
            elif hasattr(entity.dxf, 'start') and hasattr(entity.dxf, 'end'):
                geometry_info["起点"] = list(entity.dxf.start)
                geometry_info["终点"] = list(entity.dxf.end)
            elif hasattr(entity.dxf, 'center'):
                geometry_info["中心点"] = list(entity.dxf.center)
                if hasattr(entity.dxf, 'radius'):
                    geometry_info["半径"] = entity.dxf.radius
            
            # 尝试获取包围盒
            try:
                bbox = ezdxf_bbox.extents([entity])
                if bbox:
                    geometry_info["包围盒"] = {
                        "最小点": list(bbox.extmin),
                        "最大点": list(bbox.extmax),
                        "尺寸": {
                            "宽度": bbox.size.x,
                            "高度": bbox.size.y,
                            "深度": bbox.size.z
                        }
                    }
            except:
                pass
                
        except Exception as e:
            geometry_info["几何提取错误"] = str(e)
        
        return geometry_info
    
    def _extract_raw_attributes(self, entity) -> Dict:
        """提取原始DXF属性"""
        raw_attrs = {}
        try:
            # 获取所有DXF属性
            for attr_name in dir(entity.dxf):
                if not attr_name.startswith('_'):
                    try:
                        value = getattr(entity.dxf, attr_name)
                        if not callable(value):
                            raw_attrs[attr_name] = value
                    except:
                        pass
        except Exception as e:
            raw_attrs["属性提取错误"] = str(e)
        
        return raw_attrs
    
    def _extract_text_info(self, entity) -> Dict:
        """提取文本信息"""
        text_info = {}
        entity_type = entity.dxftype()
        
        try:
            if entity_type == 'TEXT':
                text_info["文本内容"] = self.clean_text(entity.dxf.text)
                text_info["文本高度"] = getattr(entity.dxf, 'height', 0)
                text_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)
                text_info["对齐方式"] = getattr(entity.dxf, 'halign', 0)
                text_info["字体样式"] = getattr(entity.dxf, 'style', '')
                
            elif entity_type == 'MTEXT':
                text_info["文本内容"] = self.clean_text(entity.plain_text())
                text_info["原始文本"] = entity.dxf.text if self.include_raw_data else ""
                text_info["字符高度"] = getattr(entity.dxf, 'char_height', 0)
                text_info["文本宽度"] = getattr(entity.dxf, 'width', 0)
                text_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)
                text_info["对齐方式"] = getattr(entity.dxf, 'attachment_point', 1)
                text_info["字体样式"] = getattr(entity.dxf, 'style', '')
                text_info["行间距"] = getattr(entity.dxf, 'line_spacing_factor', 1.0)
                
            elif entity_type in ['ATTRIB', 'ATTDEF']:
                text_info["文本内容"] = self.clean_text(entity.dxf.text)
                text_info["标签"] = getattr(entity.dxf, 'tag', '')
                text_info["提示"] = getattr(entity.dxf, 'prompt', '')
                text_info["默认值"] = getattr(entity.dxf, 'text', '')
                text_info["文本高度"] = getattr(entity.dxf, 'height', 0)
                text_info["是否可见"] = not getattr(entity.dxf, 'invisible', False)
                text_info["是否常量"] = getattr(entity.dxf, 'const', False)
                text_info["是否验证"] = getattr(entity.dxf, 'verify', False)
                
        except Exception as e:
            text_info["文本提取错误"] = str(e)
        
        return text_info
    
    def _extract_block_info(self, entity) -> Dict:
        """提取块信息"""
        block_info = {}
        
        try:
            block_info["块名称"] = getattr(entity.dxf, 'name', '')
            block_info["缩放比例"] = {
                "X": getattr(entity.dxf, 'xscale', 1.0),
                "Y": getattr(entity.dxf, 'yscale', 1.0),
                "Z": getattr(entity.dxf, 'zscale', 1.0)
            }
            block_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)
            
            # 提取块属性
            block_attributes = []
            if hasattr(entity, 'attribs'):
                for attrib in entity.attribs:
                    if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                        attr_info = {
                            "标签": getattr(attrib.dxf, 'tag', ''),
                            "值": self.clean_text(attrib.dxf.text),
                            "位置": list(attrib.dxf.insert) if self.include_coordinates else None
                        }
                        block_attributes.append(attr_info)
            
            block_info["属性列表"] = block_attributes
            block_info["属性数量"] = len(block_attributes)
            
            # 如果包含原始数据，添加块定义信息
            if self.include_raw_data and self.doc:
                try:
                    block_def = self.doc.blocks.get(entity.dxf.name)
                    if block_def:
                        block_info["块定义信息"] = {
                            "实体数量": len(block_def),
                            "基点": list(block_def.block.dxf.base_point) if self.include_coordinates else None
                        }
                except:
                    pass
                    
        except Exception as e:
            block_info["块信息提取错误"] = str(e)
        
        return block_info

    def _extract_geometric_info(self, entity) -> Dict:
        """提取几何实体信息"""
        geo_info = {}
        entity_type = entity.dxftype()

        try:
            if entity_type == 'LINE':
                geo_info["几何类型"] = "直线"
                if self.include_coordinates:
                    geo_info["起点"] = list(entity.dxf.start)
                    geo_info["终点"] = list(entity.dxf.end)
                    # 计算长度
                    import math
                    dx = entity.dxf.end[0] - entity.dxf.start[0]
                    dy = entity.dxf.end[1] - entity.dxf.start[1]
                    geo_info["长度"] = math.sqrt(dx*dx + dy*dy)
                    geo_info["角度"] = math.atan2(dy, dx) * 180 / math.pi

            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                geo_info["几何类型"] = "多段线"
                geo_info["是否闭合"] = entity.is_closed
                geo_info["顶点数量"] = len(entity)

                if self.include_coordinates:
                    vertices = []
                    for vertex in entity:
                        if hasattr(vertex, 'dxf'):
                            vertices.append(list(vertex.dxf.location))
                        else:
                            vertices.append(list(vertex))
                    geo_info["顶点列表"] = vertices

                # 计算周长/长度
                try:
                    if hasattr(entity, 'virtual_entities'):
                        total_length = 0
                        for segment in entity.virtual_entities():
                            if hasattr(segment, 'length'):
                                total_length += segment.length()
                        geo_info["总长度"] = total_length
                except:
                    pass

            elif entity_type == 'CIRCLE':
                geo_info["几何类型"] = "圆"
                if self.include_coordinates:
                    geo_info["圆心"] = list(entity.dxf.center)
                geo_info["半径"] = entity.dxf.radius
                geo_info["直径"] = entity.dxf.radius * 2
                geo_info["周长"] = 2 * 3.14159 * entity.dxf.radius
                geo_info["面积"] = 3.14159 * entity.dxf.radius * entity.dxf.radius

            elif entity_type == 'ARC':
                geo_info["几何类型"] = "圆弧"
                if self.include_coordinates:
                    geo_info["圆心"] = list(entity.dxf.center)
                geo_info["半径"] = entity.dxf.radius
                geo_info["起始角度"] = entity.dxf.start_angle
                geo_info["结束角度"] = entity.dxf.end_angle
                # 计算弧长
                import math
                angle_diff = entity.dxf.end_angle - entity.dxf.start_angle
                if angle_diff < 0:
                    angle_diff += 360
                geo_info["弧长"] = entity.dxf.radius * math.radians(angle_diff)

            elif entity_type == 'ELLIPSE':
                geo_info["几何类型"] = "椭圆"
                if self.include_coordinates:
                    geo_info["中心点"] = list(entity.dxf.center)
                    geo_info["长轴向量"] = list(entity.dxf.major_axis)
                geo_info["短长轴比"] = entity.dxf.ratio

        except Exception as e:
            geo_info["几何信息提取错误"] = str(e)

        return geo_info

    def _extract_dimension_info(self, entity) -> Dict:
        """提取标注信息"""
        dim_info = {}
        entity_type = entity.dxftype()

        try:
            dim_info["标注类型"] = entity_type

            # 标注文本
            dim_text = getattr(entity.dxf, 'text', '')
            if dim_text:
                dim_info["标注文本"] = self.clean_text(dim_text)

            # 标注样式
            dim_info["标注样式"] = getattr(entity.dxf, 'dimstyle', '')

            if self.include_coordinates:
                # 标注位置
                if hasattr(entity.dxf, 'text_midpoint'):
                    dim_info["文本位置"] = list(entity.dxf.text_midpoint)

                # 根据标注类型提取特定信息
                if entity_type == 'DIMENSION':
                    if hasattr(entity.dxf, 'defpoint'):
                        dim_info["定义点"] = list(entity.dxf.defpoint)
                    if hasattr(entity.dxf, 'defpoint2'):
                        dim_info["定义点2"] = list(entity.dxf.defpoint2)
                    if hasattr(entity.dxf, 'defpoint3'):
                        dim_info["定义点3"] = list(entity.dxf.defpoint3)

                elif entity_type == 'LEADER':
                    # 引线信息
                    if hasattr(entity, 'vertices'):
                        dim_info["引线顶点"] = [list(v) for v in entity.vertices]

            # 标注值
            if hasattr(entity.dxf, 'actual_measurement'):
                dim_info["实际测量值"] = entity.dxf.actual_measurement

        except Exception as e:
            dim_info["标注信息提取错误"] = str(e)

        return dim_info

    def _extract_hatch_info(self, entity) -> Dict:
        """提取填充信息"""
        hatch_info = {}

        try:
            hatch_info["填充类型"] = "图案填充"
            hatch_info["图案名称"] = getattr(entity.dxf, 'pattern_name', '')
            hatch_info["填充样式"] = getattr(entity.dxf, 'hatch_style', 0)
            hatch_info["是否关联"] = getattr(entity.dxf, 'associative', False)
            hatch_info["边界路径数量"] = len(entity.paths) if hasattr(entity, 'paths') else 0

            # 填充比例和角度
            hatch_info["图案比例"] = getattr(entity.dxf, 'pattern_scale', 1.0)
            hatch_info["图案角度"] = getattr(entity.dxf, 'pattern_angle', 0.0)

            if self.include_raw_data and hasattr(entity, 'paths'):
                # 边界路径信息
                boundary_info = []
                for i, path in enumerate(entity.paths):
                    path_info = {
                        "路径索引": i,
                        "路径类型": path.path_type_flags,
                        "边数量": len(path.edges) if hasattr(path, 'edges') else 0
                    }
                    boundary_info.append(path_info)
                hatch_info["边界路径详情"] = boundary_info

        except Exception as e:
            hatch_info["填充信息提取错误"] = str(e)

        return hatch_info

    def _extract_spline_info(self, entity) -> Dict:
        """提取样条曲线信息"""
        spline_info = {}

        try:
            spline_info["曲线类型"] = "样条曲线"
            spline_info["次数"] = getattr(entity.dxf, 'degree', 0)
            spline_info["控制点数量"] = len(entity.control_points) if hasattr(entity, 'control_points') else 0
            spline_info["节点数量"] = len(entity.knots) if hasattr(entity, 'knots') else 0
            spline_info["是否闭合"] = getattr(entity.dxf, 'closed', False)
            spline_info["是否周期性"] = getattr(entity.dxf, 'periodic', False)

            if self.include_coordinates and hasattr(entity, 'control_points'):
                spline_info["控制点"] = [list(pt) for pt in entity.control_points]

            if self.include_raw_data and hasattr(entity, 'knots'):
                spline_info["节点向量"] = list(entity.knots)

        except Exception as e:
            spline_info["样条信息提取错误"] = str(e)

        return spline_info

    def collect_all_entities(self):
        """收集所有实体"""
        self.all_entities = []
        self.text_entities = []
        self.geometric_entities = []
        self.dimension_entities = []
        self.block_entities = []

        print("正在收集模型空间实体...")
        # 模型空间
        for entity in self.doc.modelspace():
            entity_info = self.extract_comprehensive_entity_info(entity, "模型空间")
            self.all_entities.append(entity_info)
            self._categorize_entity(entity_info)

        print("正在收集图纸空间实体...")
        # 图纸空间
        for layout in self.doc.layouts:
            if layout.name != 'Model':
                for entity in layout:
                    entity_info = self.extract_comprehensive_entity_info(entity, f"图纸空间-{layout.name}")
                    self.all_entities.append(entity_info)
                    self._categorize_entity(entity_info)

        print("正在收集块定义实体...")
        # 块定义
        for block in self.doc.blocks:
            if not block.name.startswith('*'):
                for entity in block:
                    entity_info = self.extract_comprehensive_entity_info(entity, f"块定义-{block.name}")
                    self.all_entities.append(entity_info)
                    self._categorize_entity(entity_info)

    def _categorize_entity(self, entity_info: Dict):
        """将实体分类到不同列表中"""
        entity_type = entity_info["实体类型"]

        if entity_type in ['TEXT', 'MTEXT', 'ATTRIB', 'ATTDEF']:
            self.text_entities.append(entity_info)
        elif entity_type == 'INSERT':
            self.block_entities.append(entity_info)
        elif entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE', 'CIRCLE', 'ARC', 'ELLIPSE', 'SPLINE', 'HATCH']:
            self.geometric_entities.append(entity_info)
        elif entity_type in ['DIMENSION', 'LEADER']:
            self.dimension_entities.append(entity_info)

    def extract_document_metadata(self) -> Dict:
        """提取文档元数据"""
        metadata = {
            "文件信息": {
                "文件路径": self.dxf_path,
                "文件名": os.path.basename(self.dxf_path),
                "文件大小": os.path.getsize(self.dxf_path) if os.path.exists(self.dxf_path) else 0,
                "DXF版本": self.doc.dxfversion,
                "创建程序": getattr(self.doc.header, '$ACADVER', '未知'),
            },
            "图层信息": {},
            "线型信息": {},
            "文字样式": {},
            "标注样式": {},
            "块定义": {},
            "布局信息": {}
        }

        # 图层信息
        for layer in self.doc.layers:
            layer_info = {
                "颜色": layer.dxf.color,
                "线型": layer.dxf.linetype,
                "线宽": getattr(layer.dxf, 'lineweight', -1),
                "是否冻结": layer.is_frozen(),
                "是否锁定": layer.is_locked(),
                "是否关闭": layer.is_off(),
                "是否打印": getattr(layer.dxf, 'plot', True)
            }
            metadata["图层信息"][layer.dxf.name] = layer_info

        # 线型信息
        for linetype in self.doc.linetypes:
            linetype_info = {
                "描述": getattr(linetype.dxf, 'description', ''),
                "长度": getattr(linetype.dxf, 'length', 0),
                "元素数量": getattr(linetype.dxf, 'items', 0)
            }
            metadata["线型信息"][linetype.dxf.name] = linetype_info

        # 文字样式
        for style in self.doc.styles:
            style_info = {
                "字体文件": getattr(style.dxf, 'font', ''),
                "大字体文件": getattr(style.dxf, 'bigfont', ''),
                "高度": getattr(style.dxf, 'height', 0),
                "宽度因子": getattr(style.dxf, 'width', 1.0),
                "倾斜角度": getattr(style.dxf, 'oblique', 0),
                "是否垂直": getattr(style.dxf, 'is_vertical', False)
            }
            metadata["文字样式"][style.dxf.name] = style_info

        # 标注样式
        for dimstyle in self.doc.dimstyles:
            dimstyle_info = {
                "标注文字高度": getattr(dimstyle.dxf, 'dimtxt', 0),
                "箭头大小": getattr(dimstyle.dxf, 'dimasz', 0),
                "标注线颜色": getattr(dimstyle.dxf, 'dimclrd', 0),
                "标注文字样式": getattr(dimstyle.dxf, 'dimtxsty', '')
            }
            metadata["标注样式"][dimstyle.dxf.name] = dimstyle_info

        # 块定义信息
        for block in self.doc.blocks:
            if not block.name.startswith('*'):
                block_info = {
                    "实体数量": len(block),
                    "基点": list(block.block.dxf.base_point) if self.include_coordinates else None,
                    "描述": getattr(block.block.dxf, 'description', ''),
                    "是否匿名": block.name.startswith('*U'),
                    "实体类型统计": defaultdict(int)
                }

                # 统计块内实体类型
                for entity in block:
                    block_info["实体类型统计"][entity.dxftype()] += 1

                block_info["实体类型统计"] = dict(block_info["实体类型统计"])
                metadata["块定义"][block.name] = block_info

        # 布局信息
        for layout in self.doc.layouts:
            layout_info = {
                "布局类型": "模型空间" if layout.name == 'Model' else "图纸空间",
                "实体数量": len(layout),
                "是否活动": getattr(layout.dxf, 'tab_order', 0) == 1,
                "图纸大小": getattr(layout.dxf, 'paper_width', 0) if layout.name != 'Model' else None,
                "图纸高度": getattr(layout.dxf, 'paper_height', 0) if layout.name != 'Model' else None
            }
            metadata["布局信息"][layout.name] = layout_info

        return metadata

    def detect_drawing_sheets_advanced(self) -> List[Dict]:
        """高级图纸边框检测"""
        frames = []

        # 方法1: 查找图框实体
        print("正在检测图框实体...")
        msp = self.doc.modelspace()

        # 查找可能的图框
        potential_frames = []

        # 检查矩形和多段线
        for entity_type in ['LWPOLYLINE', 'POLYLINE', 'RECTANGLE']:
            try:
                for entity in msp.query(entity_type):
                    if hasattr(entity, 'is_closed') and entity.is_closed:
                        try:
                            bbox = ezdxf_bbox.extents([entity])
                            if bbox and bbox.size.x > 50000 and bbox.size.y > 30000:
                                potential_frames.append({
                                    "实体": entity,
                                    "包围盒": bbox,
                                    "图层": entity.dxf.layer,
                                    "实体类型": entity.dxftype()
                                })
                        except:
                            continue
            except:
                continue

        # 转换为标准格式
        for frame in potential_frames:
            bbox = frame["包围盒"]
            frames.append({
                "图框类型": "实体图框",
                "图框实体类型": frame["实体类型"],
                "图层": frame["图层"],
                "左下角": list(bbox.extmin),
                "右上角": list(bbox.extmax),
                "宽度": bbox.size.x,
                "高度": bbox.size.y,
                "面积": bbox.size.x * bbox.size.y
            })

        # 方法2: 基于文本分布的虚拟图框
        if not frames and self.text_entities:
            print("基于文本分布创建虚拟图框...")
            frames = self._create_virtual_frames()

        # 方法3: 基于所有实体的包围盒
        if not frames:
            print("基于所有实体创建全局图框...")
            try:
                all_entities = list(msp)
                if all_entities:
                    bbox = ezdxf_bbox.extents(all_entities)
                    if bbox:
                        frames.append({
                            "图框类型": "全局包围盒",
                            "左下角": list(bbox.extmin),
                            "右上角": list(bbox.extmax),
                            "宽度": bbox.size.x,
                            "高度": bbox.size.y,
                            "面积": bbox.size.x * bbox.size.y
                        })
            except:
                pass

        return frames

    def _create_virtual_frames(self) -> List[Dict]:
        """基于文本分布创建虚拟图框"""
        if not self.text_entities:
            return []

        # 提取坐标
        x_coords = []
        y_coords = []

        for text in self.text_entities:
            if self.include_coordinates and "几何信息" in text:
                geo_info = text["几何信息"]
                if "插入点" in geo_info:
                    x_coords.append(geo_info["插入点"][0])
                    y_coords.append(geo_info["插入点"][1])

        if not x_coords:
            return []

        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)

        # 检查是否有多个图稿（基于坐标分布）
        x_mid = (x_min + x_max) / 2
        y_mid = (y_min + y_max) / 2

        # 四象限分析
        quadrants = {
            "左下": [(x, y) for x, y in zip(x_coords, y_coords) if x < x_mid and y < y_mid],
            "右下": [(x, y) for x, y in zip(x_coords, y_coords) if x >= x_mid and y < y_mid],
            "左上": [(x, y) for x, y in zip(x_coords, y_coords) if x < x_mid and y >= y_mid],
            "右上": [(x, y) for x, y in zip(x_coords, y_coords) if x >= x_mid and y >= y_mid]
        }

        frames = []
        significant_quadrants = {k: v for k, v in quadrants.items() if len(v) > 10}

        if len(significant_quadrants) > 1:
            # 多个图稿
            for quad_name, coords in significant_quadrants.items():
                if coords:
                    quad_x = [c[0] for c in coords]
                    quad_y = [c[1] for c in coords]

                    frames.append({
                        "图框类型": "虚拟图框",
                        "图稿名称": f"{quad_name}图稿",
                        "左下角": [min(quad_x) - 1000, min(quad_y) - 1000],
                        "右上角": [max(quad_x) + 1000, max(quad_y) + 1000],
                        "宽度": max(quad_x) - min(quad_x) + 2000,
                        "高度": max(quad_y) - min(quad_y) + 2000,
                        "文本点数量": len(coords)
                    })
        else:
            # 单个图稿
            frames.append({
                "图框类型": "虚拟图框",
                "图稿名称": "主图稿",
                "左下角": [x_min - 1000, y_min - 1000],
                "右上角": [x_max + 1000, y_max + 1000],
                "宽度": x_max - x_min + 2000,
                "高度": y_max - y_min + 2000,
                "文本点数量": len(x_coords)
            })

        return frames

    def analyze_content_by_keywords(self) -> Dict:
        """基于关键词分析内容"""
        keyword_analysis = {
            "消防系统": {
                "关键词": ["消防", "火灾", "报警", "喷淋", "烟感", "温感", "手报", "声光"],
                "匹配实体": [],
                "统计": defaultdict(int)
            },
            "电气系统": {
                "关键词": ["电源", "配电", "照明", "插座", "开关", "线路", "电缆", "导线"],
                "匹配实体": [],
                "统计": defaultdict(int)
            },
            "设备编号": {
                "关键词": ["CAM", "CAC", "MJ", "FCUN", "FTBN", "#", "@"],
                "匹配实体": [],
                "统计": defaultdict(int)
            },
            "尺寸标注": {
                "关键词": ["mm", "m", "×", "x", "Φ", "φ"],
                "匹配实体": [],
                "统计": defaultdict(int)
            }
        }

        # 分析所有文本实体
        for entity in self.text_entities:
            if "文本内容" in entity:
                text_content = entity["文本内容"]

                for category, info in keyword_analysis.items():
                    for keyword in info["关键词"]:
                        if keyword in text_content:
                            info["匹配实体"].append({
                                "文本": text_content,
                                "图层": entity.get("图层", ""),
                                "实体类型": entity.get("实体类型", ""),
                                "空间类型": entity.get("空间类型", "")
                            })
                            info["统计"][entity.get("图层", "未知图层")] += 1
                            break

        # 转换统计信息
        for category in keyword_analysis:
            keyword_analysis[category]["统计"] = dict(keyword_analysis[category]["统计"])
            keyword_analysis[category]["匹配数量"] = len(keyword_analysis[category]["匹配实体"])

        return keyword_analysis

    def generate_comprehensive_output(self) -> Dict:
        """生成全面的结构化输出"""
        if not self.load_document():
            return {"错误": "无法加载DXF文件"}

        print("开始全面解析DXF文件...")

        # 收集所有实体
        self.collect_all_entities()

        # 提取文档元数据
        print("正在提取文档元数据...")
        metadata = self.extract_document_metadata()

        # 检测图纸边框
        print("正在检测图纸边框...")
        drawing_sheets = self.detect_drawing_sheets_advanced()

        # 关键词分析
        print("正在进行关键词分析...")
        keyword_analysis = self.analyze_content_by_keywords()

        # 构建全面输出
        comprehensive_output = {
            "解析配置": {
                "包含坐标": self.include_coordinates,
                "包含原始数据": self.include_raw_data,
                "解析时间": None  # 可以添加时间戳
            },
            "文档元数据": metadata,
            "实体统计": {
                "总实体数": len(self.all_entities),
                "文本实体数": len(self.text_entities),
                "几何实体数": len(self.geometric_entities),
                "标注实体数": len(self.dimension_entities),
                "块实体数": len(self.block_entities),
                "按类型统计": defaultdict(int),
                "按图层统计": defaultdict(int),
                "按空间统计": defaultdict(int)
            },
            "图稿信息": {
                "图稿数量": len(drawing_sheets),
                "图稿列表": drawing_sheets
            },
            "内容分析": keyword_analysis,
            "详细实体数据": {
                "文本实体": self.text_entities if self.include_raw_data else [],
                "几何实体": self.geometric_entities if self.include_raw_data else [],
                "标注实体": self.dimension_entities if self.include_raw_data else [],
                "块实体": self.block_entities if self.include_raw_data else []
            }
        }

        # 统计信息
        for entity in self.all_entities:
            comprehensive_output["实体统计"]["按类型统计"][entity["实体类型"]] += 1
            comprehensive_output["实体统计"]["按图层统计"][entity["图层"]] += 1
            comprehensive_output["实体统计"]["按空间统计"][entity["空间类型"]] += 1

        # 转换defaultdict为普通dict
        comprehensive_output["实体统计"]["按类型统计"] = dict(comprehensive_output["实体统计"]["按类型统计"])
        comprehensive_output["实体统计"]["按图层统计"] = dict(comprehensive_output["实体统计"]["按图层统计"])
        comprehensive_output["实体统计"]["按空间统计"] = dict(comprehensive_output["实体统计"]["按空间统计"])

        return comprehensive_output


def process_dxf_comprehensive(input_path: Union[str, Path],
                            output_base: Optional[str] = None,
                            include_coordinates: bool = True,
                            include_raw_data: bool = True) -> Dict[str, Any]:
    """
    全面处理DXF文件（支持单文件和批量处理）

    Args:
        input_path: 输入路径（文件或文件夹）
        output_base: 输出基础目录（可选）
        include_coordinates: 是否包含坐标信息
        include_raw_data: 是否包含原始数据

    Returns:
        处理结果统计
    """
    input_path = Path(input_path)

    # 验证输入路径
    if not input_path.exists():
        raise FileNotFoundError(f"输入路径不存在: {input_path}")

    # 获取DXF文件列表
    dxf_files = []
    if input_path.is_file():
        if input_path.suffix.lower() == '.dxf':
            dxf_files.append(input_path)
        else:
            raise ValueError(f"输入文件不是DXF格式: {input_path}")
    elif input_path.is_dir():
        dxf_files = list(input_path.rglob('*.dxf')) + list(input_path.rglob('*.DXF'))
        if not dxf_files:
            raise ValueError(f"在目录 {input_path} 中未找到DXF文件")

    # 创建输出目录
    if output_base:
        output_dir = Path(output_base)
    else:
        if input_path.is_file():
            output_dir = input_path.parent / f"{input_path.stem}_parsed_v3"
        else:
            output_dir = input_path.parent / f"{input_path.name}_parsed_v3"

    output_dir.mkdir(parents=True, exist_ok=True)

    print(f"找到 {len(dxf_files)} 个DXF文件")
    print(f"输出目录: {output_dir}")
    print(f"解析配置: 坐标={include_coordinates}, 原始数据={include_raw_data}")

    # 处理统计
    results = {
        "成功": 0,
        "失败": 0,
        "总计": len(dxf_files),
        "失败文件": [],
        "配置": {
            "包含坐标": include_coordinates,
            "包含原始数据": include_raw_data
        }
    }

    # 批量处理
    for dxf_file in tqdm(dxf_files, desc="全面解析DXF文件"):
        try:
            # 创建解析器
            parser = DXFComprehensiveParser(
                str(dxf_file),
                include_coordinates=include_coordinates,
                include_raw_data=include_raw_data
            )

            # 生成输出文件名
            output_filename = f"{dxf_file.stem}.json"
            output_path = output_dir / output_filename

            # 执行解析
            result = parser.generate_comprehensive_output()

            if "错误" in result:
                results["失败"] += 1
                results["失败文件"].append(str(dxf_file))
                print(f"✗ 解析失败: {dxf_file.name} - {result['错误']}")
                continue

            # 保存结果
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            results["成功"] += 1
            print(f"✓ 成功解析: {dxf_file.name}")

        except Exception as e:
            results["失败"] += 1
            results["失败文件"].append(str(dxf_file))
            print(f"✗ 解析异常: {dxf_file.name} - {e}")

    # 打印最终统计
    print(f"\n=== 全面解析完成 ===")
    print(f"总文件数: {results['总计']}")
    print(f"成功解析: {results['成功']}")
    print(f"解析失败: {results['失败']}")

    if results["失败文件"]:
        print(f"\n失败文件列表:")
        for failed_file in results["失败文件"]:
            print(f"  - {failed_file}")

    return results


def main():
    """主函数"""
    import sys

    # 示例用法
    if len(sys.argv) > 1:
        input_path = sys.argv[1]
        output_base = sys.argv[2] if len(sys.argv) > 2 else None
        include_coordinates = sys.argv[3].lower() == 'true' if len(sys.argv) > 3 else True
        include_raw_data = sys.argv[4].lower() == 'true' if len(sys.argv) > 4 else True
    else:
        # 默认示例
        input_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/中广核/图纸格式转化'
        output_base = None
        include_coordinates = True
        include_raw_data = True

    try:
        results = process_dxf_comprehensive(
            input_path,
            output_base,
            include_coordinates=include_coordinates,
            include_raw_data=include_raw_data
        )

        if results["总计"] > 0:
            success_rate = (results["成功"] / results["总计"]) * 100
            print(f"\n解析成功率: {success_rate:.1f}%")

        return 0

    except Exception as e:
        print(f"程序执行出错: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
